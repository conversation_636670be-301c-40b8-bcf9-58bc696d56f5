#!/usr/bin/env python3
import os
import sys
import asyncio
import threading
from typing import Dict, Any, Optional, List
from concurrent.futures import ThreadPoolExecutor

import time
import json
from datetime import datetime
from pathlib import Path
from src.agent.agent import AgenticRAG
from src.logging.logger import logging
from src.logging.call_logger import call_logger
from src.exception import CustomException
from src.utils.language_detection import LanguageDetector
from src.tools.vector_database_tool import VectorDatabaseTool
from src.tools.web_search_tool import WebSearchTool

from livekit.agents import AgentSession, Agent, JobContext, llm, stt
from livekit.plugins import silero, groq, cartesia
from livekit import rtc
from dotenv import load_dotenv

# FastAPI imports (kept for later use; server start commented out)
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel

load_dotenv()

# FastAPI Configuration models
class ModelConfig(BaseModel):
    model: str
    api_key: str

class SettingsConfig(BaseModel):
    stt: ModelConfig
    llm: ModelConfig
    tts: ModelConfig

class ServerStatus(BaseModel):
    status: str
    pid: Optional[int] = None
    uptime: Optional[float] = None

# ---------- Config helpers ----------
def load_model_config():
    """Load model configuration from saved settings"""
    config_file = Path("config/model_settings.json")
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                logging.info(f"Loaded model configuration: {config}")
                return config
        except Exception as e:
            logging.error(f"Error loading model config: {e}")

    # Default configuration
    default_config = {
        "stt": {"model": "whisper-large-v3", "api_key": os.getenv("GROQ_API_KEY", "")},
        "llm": {"model": "llama3-70b-8192", "api_key": os.getenv("GROQ_API_KEY", "")},
        "tts": {"model": "sonic-2", "api_key": os.getenv("CARTESIA_API_KEY", "")}
    }
    logging.info(f"Using default model configuration: {default_config}")
    return default_config

def load_settings() -> Optional[Dict[str, Any]]:
    """Load saved model settings from file"""
    config_file_path = "config/model_settings.json"
    try:
        if os.path.exists(config_file_path):
            with open(config_file_path, 'r') as f:
                return json.load(f)
    except Exception as e:
        logging.error(f"Error loading settings: {e}")
    return None

def save_settings(settings: Dict[str, Any]) -> bool:
    """Save model settings to file"""
    config_file_path = "config/model_settings.json"
    try:
        with open(config_file_path, 'w') as f:
            json.dump(settings, f, indent=2)
        return True
    except Exception as e:
        logging.error(f"Error saving settings: {e}")
        return False

# Load configuration at import time (safe)
MODEL_CONFIG = load_model_config()

# ---------- Globals ----------
session_language = None
session_language_config = None
current_call_id = None
transcription_logger = None

# IMPORTANT: do NOT initialize heavy components at module import time.
# Instead we define globals and initializer functions that main() will call.
rag_agent: Optional[AgenticRAG] = None
language_detector: Optional[LanguageDetector] = None
vector_tool_instance: Optional[VectorDatabaseTool] = None
web_tool_instance: Optional[WebSearchTool] = None



# FastAPI app initialization
app = FastAPI(
    title="AI Voice System Backend",
    description="FastAPI backend for AI Voice Conversation Interface",
    version="1.0.0"
)

# CORS middleware - Allow frontend connections
origins = [
    "https://voice-agent-front-end11.vercel.app",
    "https://voice-agent-front-end11.vercel.app",
    "https://voice-agent-front-end11-git-main-llaisolutions-projects.vercel.app",
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5174",
    "http://localhost:8080",
    "http://127.0.0.1:8080",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Ensure config directory exists
os.makedirs("config", exist_ok=True)

# Add request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    print(f"Incoming request: {request.method} {request.url}")
    print(f"Headers: {dict(request.headers)}")

    response = await call_next(request)

    process_time = time.time() - start_time
    print(f"Response: {response.status_code} - Process time: {process_time:.2f}s")

    return response

# Global OPTIONS handler for CORS preflight
@app.options("/{path:path}")
async def options_handler(path: str):
    """Handle OPTIONS requests for CORS preflight"""
    return JSONResponse(
        status_code=200,
        content={"message": "OK"},
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
            "Access-Control-Allow-Headers": "*",
        }
    )

# FastAPI Health and Configuration Endpoints
@app.get("/api/health")
@app.options("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "server_status": "running"
    }

# Model Configuration Endpoints
@app.post("/api/save-settings")
async def save_model_settings(settings: SettingsConfig):
    """Save model configuration settings"""
    try:
        settings_dict = {
            "stt": {"model": settings.stt.model, "api_key": settings.stt.api_key},
            "llm": {"model": settings.llm.model, "api_key": settings.llm.api_key},
            "tts": {"model": settings.tts.model, "api_key": settings.tts.api_key},
            "timestamp": datetime.now().isoformat()
        }

        if save_settings(settings_dict):
            logging.info(f"Settings saved successfully: {settings_dict}")
            return {"status": "success", "message": "Settings saved successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to save settings")

    except Exception as e:
        logging.error(f"Error saving settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/load-settings")
async def load_model_settings():
    """Load saved model configuration settings"""
    try:
        settings = load_settings()
        if settings:
            return {"status": "success", "settings": settings}
        else:
            return {"status": "success", "settings": None, "message": "No saved settings found"}
    except Exception as e:
        logging.error(f"Error loading settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/available-models")
async def get_available_models():
    """Get available models for STT, LLM, and TTS"""
    try:
        available_models = {
            "stt": {
                "models": [
                    {
                        "value": "whisper-large-v3-turbo",
                        "label": "Whisper Large V3 Turbo (Groq)",
                        "provider": "groq",
                        "endpoint": "https://api.groq.com/openai/v1/audio/translations"
                    },
                    {
                        "value": "whisper-large-v3",
                        "label": "Whisper Large V3 (Groq)",
                        "provider": "groq",
                        "endpoint": "https://api.groq.com/openai/v1/audio/translations"
                    }
                ],
                "default": "whisper-large-v3-turbo"
            },
            "llm": {
                "models": [
                    {
                        "value": "llama3-8b-8192",
                        "label": "Llama 3 8B (Groq)",
                        "provider": "groq",
                        "endpoint": "https://api.groq.com/openai/v1/chat/completions"
                    },
                    {
                        "value": "llama-3.1-8b-instant",
                        "label": "Llama 3.1 8B Instant (Groq)",
                        "provider": "groq",
                        "endpoint": "https://api.groq.com/openai/v1/chat/completions"
                    },
                    {
                        "value": "llama/llama-4-maverick-17b-128e-instruct",
                        "label": "Llama 4 Maverick 17B (Groq)",
                        "provider": "groq",
                        "endpoint": "https://api.groq.com/openai/v1/chat/completions"
                    }
                ],
                "default": "llama3-8b-8192"
            },
            "tts": {
                "models": [
                    {
                        "value": "sonic-2",
                        "label": "Sonic 2 (Cartesia)",
                        "provider": "cartesia",
                        "endpoint": "https://api.cartesia.ai/tts/bytes"
                    }
                ],
                "default": "sonic-2"
            }
        }

        return {"status": "success", "models": available_models}
    except Exception as e:
        logging.error(f"Error fetching available models: {e}")
        raise HTTPException(status_code=500, detail=str(e))




@app.get("/api/call-logs")
async def get_call_logs():
    """Get all call logs"""
    try:
        call_logs_dir = Path("call_logs")
        if not call_logs_dir.exists():
            return {"status": "success", "logs": [], "message": "No call logs directory found"}

        logs = []

        # Get all call log files
        for log_file in call_logs_dir.glob("call_*.txt"):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Extract basic info from filename and content
                filename = log_file.name
                file_stats = log_file.stat()

                logs.append({
                    "filename": filename,
                    "created_time": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                    "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                    "size": file_stats.st_size,
                    "preview": content[:200] + "..." if len(content) > 200 else content
                })

            except Exception as e:
                logging.error(f"Error reading log file {log_file}: {e}")
                continue

        # Sort by creation time (newest first)
        logs.sort(key=lambda x: x["created_time"], reverse=True)

        return {"status": "success", "logs": logs}

    except Exception as e:
        logging.error(f"Error fetching call logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/call-logs/{filename}")
async def get_call_log_detail(filename: str):
    """Get detailed content of a specific call log"""
    try:
        call_logs_dir = Path("call_logs")
        log_file = call_logs_dir / filename

        if not log_file.exists():
            raise HTTPException(status_code=404, detail="Call log file not found")

        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()

        file_stats = log_file.stat()

        return {
            "status": "success",
            "filename": filename,
            "content": content,
            "created_time": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
            "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
            "size": file_stats.st_size
        }

    except Exception as e:
        logging.error(f"Error fetching call log detail: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/transcriptions")
async def get_recent_transcriptions():
    """Get recent transcriptions from call logs"""
    try:
        call_logs_dir = Path("call_logs")
        if not call_logs_dir.exists():
            return {"status": "success", "transcriptions": []}

        transcriptions = []

        # Get recent transcript files
        transcript_files = list(call_logs_dir.glob("call_transcript_*.txt")) + \
                          list(call_logs_dir.glob("console_transcript_*.txt"))

        # Sort by modification time (newest first)
        transcript_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

        # Get the most recent 5 files
        for transcript_file in transcript_files[:5]:
            try:
                with open(transcript_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                file_stats = transcript_file.stat()

                transcriptions.append({
                    "filename": transcript_file.name,
                    "content": content,
                    "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                    "type": "call" if "call_transcript" in transcript_file.name else "console"
                })

            except Exception as e:
                logging.error(f"Error reading transcript file {transcript_file}: {e}")
                continue

        return {"status": "success", "transcriptions": transcriptions}

    except Exception as e:
        logging.error(f"Error fetching transcriptions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ---------- Initialization helpers ----------
def initialize_rag():
    """Initialize RAG agent and language detector once (called from main only)."""
    global rag_agent, language_detector
    print("🚀 Initializing RAG agent and language detector at startup...")
    startup_time = time.time()
    try:
        rag_agent = AgenticRAG()
        language_detector = LanguageDetector()
        startup_duration = time.time() - startup_time
        print(f"✅ RAG agent and language detector initialized successfully in {startup_duration:.2f}s!")
    except Exception as e:
        print(f"⚠️ Failed to initialize RAG agent: {e}")
        rag_agent = None
        language_detector = None

def precreate_tools():
    """Pre-create vector and web tools using the preloaded RAG agent (call from main)."""
    global vector_tool_instance, web_tool_instance, rag_agent
    print("🔧 Pre-creating tools with preloaded RAG agent...")
    try:
        if rag_agent is not None:
            # VectorDatabaseTool expects an LLM from the rag_agent; adapt as needed
            vector_tool_instance = VectorDatabaseTool(rag_agent.llm)
            print("✅ Vector database tool created with preloaded RAG agent")
        else:
            print("⚠️ RAG agent not available, vector tool will be created on demand")
            vector_tool_instance = None

        web_tool_instance = WebSearchTool()
        print("✅ Web search tool created")
        print("✅ All tools pre-created successfully")
    except Exception as e:
        print(f"⚠️ Tool pre-creation failed: {e}")
        vector_tool_instance = None
        web_tool_instance = None

def get_rag_agent():
    """Return the preloaded rag_agent (initialize lazily if needed)."""
    global rag_agent
    if rag_agent is None:
        # Warn that lazy init is happening. Avoid this by pre-initializing in main.
        print("⚠️ RAG agent not preloaded, initializing now (this may cause delay)...")
        initialize_rag()
    return rag_agent

# ---------- Language detection helper ----------
def simple_language_detection(query: str):
    """Simple language detection (lazy-initialize detector)."""
    try:
        global language_detector
        if language_detector is None:
            language_detector = LanguageDetector()
        return language_detector.detect_language(query)
    except Exception as e:
        logging.error(f"Language detection error: {e}")
        return 'en', 'English', {'name': 'English'}


#----------------LIVE TRANSCRIPTION AUDIO----------------   
@app.get("/api/live-transcription")
@app.options("/api/live-transcription")
async def get_live_transcription():
    """Get the most recent live transcription"""
    try:
        call_logs_dir = Path("call_logs")
        if not call_logs_dir.exists():
            return {"status": "success", "transcription": None}

        # Find the most recent transcript file
        transcript_files = list(call_logs_dir.glob("call_transcript_*.txt")) + \
                          list(call_logs_dir.glob("console_transcript_*.txt"))

        if not transcript_files:
            return {"status": "success", "transcription": None}

        # Get the most recently modified file
        latest_file = max(transcript_files, key=lambda x: x.stat().st_mtime)

        with open(latest_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract the last few lines for live display
        lines = content.strip().split('\n')
        recent_lines = lines[-10:] if len(lines) > 10 else lines

        return {
            "status": "success",
            "transcription": {
                "filename": latest_file.name,
                "recent_content": '\n'.join(recent_lines),
                "full_content": content,
                "last_modified": datetime.fromtimestamp(latest_file.stat().st_mtime).isoformat()
            }
        }

    except Exception as e:
        logging.error(f"Error fetching live transcription: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/active-calls")
async def get_active_calls():
    """Get currently active calls"""
    try:
        active_calls = call_logger.get_active_calls()

        # Convert CallInfo objects to dictionaries for JSON serialization
        calls_data = {}
        for call_id, call_info in active_calls.items():
            calls_data[call_id] = {
                "call_id": call_info.call_id,
                "caller_phone_number": call_info.caller_phone_number,
                "trunk_phone_number": call_info.trunk_phone_number,
                "call_status": call_info.call_status,
                "start_time": call_info.start_time,
                "language_detected": call_info.language_detected,
                "transcription_count": len(call_info.transcriptions),
                "response_count": len(call_info.agent_responses)
            }

        return {"status": "success", "active_calls": calls_data}

    except Exception as e:
        logging.error(f"Error fetching active calls: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/audio-levels")
@app.options("/api/audio-levels")
async def get_audio_levels():
    """Get real-time audio levels from active calls"""
    try:
        active_calls = call_logger.get_active_calls()

        # Calculate audio level based on call activity
        audio_level = 0
        call_activity = False

        if active_calls:
            call_activity = True
            # Get the most recent call
            latest_call = max(active_calls.values(), key=lambda x: x.start_time)

            # Simulate audio level based on recent activity
            recent_transcriptions = latest_call.transcriptions[-5:] if latest_call.transcriptions else []
            recent_responses = latest_call.agent_responses[-5:] if latest_call.agent_responses else []

            # Check if there's been recent activity (within last 30 seconds)
            now = datetime.now().isoformat()
            recent_activity = False

            for trans in recent_transcriptions:
                if trans.get("timestamp"):
                    trans_time = datetime.fromisoformat(trans["timestamp"].replace('Z', '+00:00'))
                    if (datetime.now(trans_time.tzinfo) - trans_time).seconds < 30:
                        recent_activity = True
                        break

            if not recent_activity:
                for resp in recent_responses:
                    if resp.get("timestamp"):
                        resp_time = datetime.fromisoformat(resp["timestamp"].replace('Z', '+00:00'))
                        if (datetime.now(resp_time.tzinfo) - resp_time).seconds < 30:
                            recent_activity = True
                            break

            if recent_activity:
                # Simulate realistic audio levels during active conversation
                import random
                audio_level = random.randint(20, 85)  # Active conversation levels
            else:
                # Low level for active call but no recent speech
                audio_level = random.randint(0, 15)
        else:
            # No active calls
            audio_level = 0

        return {
            "status": "success",
            "audio_level": audio_level,
            "has_active_calls": call_activity,
            "active_call_count": len(active_calls),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logging.error(f"Error fetching audio levels: {e}")
        return {
            "status": "success",
            "audio_level": 0,
            "has_active_calls": False,
            "active_call_count": 0,
            "timestamp": datetime.now().isoformat()
        }


# ---------- Transcription logger ----------
class TranscriptionLogger:
    """Enhanced logger for user and agent transcriptions during calls"""

    def __init__(self, log_directory: str = "call_logs"):
        self.log_directory = log_directory
        os.makedirs(self.log_directory, exist_ok=True)
        self.call_transcript_file = None
        self.console_transcript_file = None
        self.current_file = None

    def start_call_transcription(self, call_id: str):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"call_transcript_{call_id}_{timestamp}.txt"
        self.call_transcript_file = os.path.join(self.log_directory, filename)
        self.current_file = self.call_transcript_file

        with open(self.call_transcript_file, 'w', encoding='utf-8') as f:
            f.write(f"=== CALL TRANSCRIPTION LOG ===\n")
            f.write(f"Call ID: {call_id}\n")
            f.write(f"Started: {datetime.now().isoformat()}\n")
            f.write("=" * 50 + "\n\n")

        print(f"📝 Started call transcription logging: {filename}")

    def start_console_transcription(self):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"console_transcript_{timestamp}.txt"
        self.console_transcript_file = os.path.join(self.log_directory, filename)
        self.current_file = self.console_transcript_file

        with open(self.console_transcript_file, 'w', encoding='utf-8') as f:
            f.write(f"=== CONSOLE TRANSCRIPTION LOG ===\n")
            f.write(f"Started: {datetime.now().isoformat()}\n")
            f.write("=" * 50 + "\n\n")

        print(f"📝 Started console transcription logging: {filename}")

    def log_transcription(self, text: str | list, is_final: bool = True, speaker: str = "USER"):
        if isinstance(text, list):
            text = " ".join(str(item) for item in text)
        elif not isinstance(text, str):
            text = str(text)

        if not text or not text.strip():
            return

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        speaker_emoji = "👤" if speaker == "USER" else "🤖"
        log_entry = f"[{timestamp}] {speaker_emoji} {speaker}: {text.strip()}\n"
        target_file = self.current_file

        if target_file and is_final:
            try:
                with open(target_file, 'a', encoding='utf-8') as f:
                    f.write(log_entry)
                    f.flush()
                print(f"📝 {speaker_emoji} {speaker}: {text.strip()}")
            except Exception as e:
                print(f"❌ Error writing transcription: {e}")

    def log_agent_response(self, text: str):
        self.log_transcription(text, is_final=True, speaker="AGENT")

    def log_user_speech(self, text: str):
        self.log_transcription(text, is_final=True, speaker="USER")

    def close_transcription(self):
        if self.current_file:
            try:
                with open(self.current_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n=== TRANSCRIPTION ENDED ===\n")
                    f.write(f"Ended: {datetime.now().isoformat()}\n")
                print(f"📝 Transcription session closed: {self.current_file}")
            except Exception as e:
                print(f"❌ Error closing transcription: {e}")

# ---------- Agent session enhancements ----------
class EnhancedCallLoggingSession(AgentSession):
    """Enhanced session with transcription logging and proper call lifecycle management"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.call_active = False

    async def start(self, agent, room):
        global current_call_id, transcription_logger

        @self.on("user_speech_committed")
        def on_user_speech(event):
            # event may contain participant info; log only if present
            if transcription_logger and hasattr(event, 'user_transcript') and event.user_transcript:
                transcription_logger.log_user_speech(event.user_transcript)
                if current_call_id:
                    call_logger.log_user_transcription(current_call_id, event.user_transcript)

        @self.on("agent_speech_committed")
        def on_agent_speech(event):
            if transcription_logger and hasattr(event, 'agent_transcript') and event.agent_transcript:
                transcription_logger.log_agent_response(event.agent_transcript)
                if current_call_id:
                    call_logger.log_agent_response(current_call_id, event.agent_transcript)

        @self.on("conversation_item_added")
        def on_conversation_item(event):
            if transcription_logger and hasattr(event, 'item'):
                item = event.item
                if hasattr(item, 'content') and item.content:
                    speaker = "USER" if hasattr(item, 'role') and item.role == 'user' else "AGENT"
                    transcription_logger.log_transcription(item.content, is_final=True, speaker=speaker)

        self.call_active = True
        await super().start(agent=agent, room=room)

    async def generate_reply(self, **kwargs):
        global current_call_id, transcription_logger
        reply = await super().generate_reply(**kwargs)

        if reply:
            response_text = None
            if hasattr(reply, 'content') and reply.content:
                response_text = reply.content
            elif hasattr(reply, 'text') and reply.text:
                response_text = reply.text
            elif isinstance(reply, str):
                response_text = reply

            if response_text and transcription_logger:
                transcription_logger.log_agent_response(response_text)
            if response_text and current_call_id:
                call_logger.log_agent_response(current_call_id, response_text)

        return reply

    async def aclose(self):
        global transcription_logger
        try:
            await super().aclose()
        finally:
            self.call_active = False
            if transcription_logger:
                transcription_logger.close_transcription()

# ---------- Tools as llm.function_tool wrappers ----------
FAST_RESPONSES = {
    'hi': {
        'no_results': "Mere paas is technical sawal ka jawaab nahi hai.",
        'error': "Knowledge base mein kuch technical problem hai."
    },
    'ta': {
        'no_results': "Enakku indha technical kelvikku information illa.",
        'error': "Knowledge base la konjam technical problem irukku."
    },
    'te': {
        'no_results': "Naa daggara ee technical prashnaku information ledu.",
        'error': "Knowledge base lo konni technical samasyalu unnaayi."
    },
    'de': {
        'no_results': "Ich habe keine technischen Informationen dazu.",
        'error': "Es gibt ein technisches Problem mit der Wissensdatenbank."
    },
    'fr': {
        'no_results': "Je n'ai pas d'informations techniques à ce sujet.",
        'error': "Il y a un problème technique avec la base de connaissances."
    },
    'en': {
        'no_results': "I don't have technical information about this.",
        'error': "Technical issue with knowledge base."
    }
}

def create_vector_database_tool():
    @llm.function_tool(
        name="vector_database_search",
        description="Search technical documentation. Use ONLY for: ABB switchgear, transformers, RAG, NLP, technical docs.IT provides you the relevant things according to you query , try to utilize the information and provide properly formatted to the point answer, Returns complete answer - no additional tools needed."
    )
    async def vector_database_search(query: str) -> str:
        start_time = time.time()
        try:
            global session_language, session_language_config, vector_tool_instance

            if vector_tool_instance is None:
                print("⚠️ Vector tool not preloaded, creating on demand...")
                rag = get_rag_agent()
                vector_tool_instance = VectorDatabaseTool(rag.llm)

            if session_language is None:
                lang_code, lang_name, lang_config = simple_language_detection(query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"🌍 Language detected: {lang_name} ({lang_code})")

            result = vector_tool_instance.search_documents(
                query,
                session_language or 'en'
            )

            lang = session_language or 'en'
            if result.get('is_relevant'):
                response = f"FINAL_ANSWER: {result.get('results')}"
                logging.info(f"✅ Vector search completed: {time.time() - start_time:.2f}s")
            else:
                response = f"FINAL_ANSWER: {FAST_RESPONSES.get(lang, FAST_RESPONSES['en'])['no_results']}"
                logging.info(f"❌ No results found: {time.time() - start_time:.2f}s")

            if lang != 'en' and session_language_config:
                lang_name = session_language_config.get('name', '')
                if lang_name:
                    response = response.replace("FINAL_ANSWER:", f"FINAL_ANSWER [{lang_name}]:")

            return response

        except Exception as e:
            logging.error(f"Vector tool error: {e}")
            lang = session_language or 'en'
            return f"FINAL_ANSWER: {FAST_RESPONSES.get(lang, FAST_RESPONSES['en'])['error']}"

    return vector_database_search

def create_web_search_tool():
    @llm.function_tool(
        name="web_search",
        description="Search current information. Use for: Chief Ministers, Prime Ministers, current events, news, weather. Returns complete answer - no additional tools needed."
    )
    async def web_search(query: str) -> str:
        start_time = time.time()
        try:
            global session_language, session_language_config, web_tool_instance

            if web_tool_instance is None:
                print("⚠️ Web tool not preloaded, creating on demand...")
                web_tool_instance = WebSearchTool()

            if session_language is None:
                lang_code, lang_name, lang_config = simple_language_detection(query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"🌍 Language detected: {lang_name} ({lang_code})")

            enhanced_query = f"{query} as of 2025"
            logging.info(f"Enhanced web search query: {enhanced_query}")

            result = web_tool_instance.search_web(
                enhanced_query,
                session_language or 'en'
            )

            response = f"FINAL_ANSWER: {result.get('results')}"
            logging.info(f"🌐 Web search completed: {time.time() - start_time:.2f}s, {result.get('result_count')} results")

            lang = session_language or 'en'
            if lang != 'en' and session_language_config:
                lang_name = session_language_config.get('name', '')
                if lang_name:
                    response = response.replace("FINAL_ANSWER:", f"FINAL_ANSWER [{lang_name}]:")

            return response

        except Exception as e:
            logging.error(f"Web search error: {e}")
            lang = session_language or 'en'
            web_errors = {
                'hi': "Web search mein technical problem hai.",
                'ta': "Web search la technical problem irukku.",
                'te': "Web search lo technical problem undi.",
                'de': "Technisches Problem bei der Websuche.",
                'fr': "Problème technique avec la recherche web.",
                'en': "Technical issue with web search."
            }
            return f"FINAL_ANSWER: {web_errors.get(lang, web_errors['en'])}"

    return web_search

# ---------- UltraFastLanguageAgent ----------
def get_language_specific_instructions(lang_code: str, lang_config: dict) -> str:
    if lang_code == 'en':
        return """
You are a helpful voice AI assistant. Respond in clear, natural English.

🚨 CRITICAL RESPONSE RULES 🚨

1. SINGLE TOOL CALL ONLY:
   - Use ONLY ONE tool per user question
   - NEVER call multiple tools for the same query
   - When tool returns "FINAL_ANSWER:", extract and speak only the answer part
   - Do NOT mention tool names when speaking
You are a helpful voice AI assistant responding in {lang_name}.

🚨 CRITICAL RESPONSE RULES 🚨

1. SINGLE TOOL CALL ONLY:
   - Use ONLY ONE tool per user question
   - NEVER call multiple tools for the same query
   - When tool returns "FINAL_ANSWER:", extract and speak only the answer part in {lang_name}
   - Do NOT mention tool names when speaking

2. LANGUAGE RULES:
   - Respond ONLY in {lang_name} using English alphabet transliteration
   - NEVER use English words or switch languages
   - Example greeting: '{sample_greeting}'
   - Maintain {lang_name} throughout conversation

3. TOOL SELECTION (Use ONE only):
   - Chief Minister/Prime Minister questions → web_search
   - Current events, news, weather → web_search
   - ABB switchgear, transformers, RAG, NLP → vector_database_search
   - Simple greetings → Direct response in {lang_name} (NO TOOLS)

4. RESPONSE FORMAT:
   - Extract answer from "FINAL_ANSWER: [content]"
   - Translate to {lang_name} transliteration
   - Speak naturally without mentioning tools
   - Keep responses concise and conversational

EXAMPLES:
- Greeting: "{sample_greeting}" (NO TOOLS)
- "Chief Minister kaun hai?" → Use web_search ONCE → Answer in {lang_name}
- "ABB switchgear kya hai?" → Use vector_database_search ONCE → Answer in {lang_name}

REMEMBER: ONE tool call maximum, extract answer, respond in {lang_name} without mentioning tools.

"""
    lang_name = lang_config.get('name', 'Unknown')
    sample_greeting = lang_config.get('sample_phrases', {}).get('greeting', '')
    sample_error = lang_config.get('sample_phrases', {}).get('error', '')

    return f"""
You are a helpful voice AI assistant responding in {lang_name}.
... (keep your multilingual template here) ...
"""

class UltraFastLanguageAgent(Agent):
    def __init__(self, llm, tools):
        initial_instructions = """⚡ MULTILINGUAL AI ASSISTANT ⚡
... (keep your full initial instructions here) ...
"""
        super().__init__(llm=llm, tools=tools, instructions=initial_instructions)
        self.current_language = None
        self.language_config = None
        self.language_locked = False

    async def _handle_user_message(self, message):
        start_time = time.time()
        global session_language, session_language_config, current_call_id, transcription_logger

        if message.content:
            if transcription_logger:
                transcription_logger.log_user_speech(message.content)
            if current_call_id:
                call_logger.log_user_transcription(current_call_id, message.content)

            if session_language is None:
                lang_code, lang_name, lang_config = simple_language_detection(message.content)
                if lang_code != 'unknown':
                    session_language = lang_code
                    session_language_config = lang_config
                    self.current_language = lang_code
                    self.language_config = lang_config
                    self.language_locked = True
                    logging.info(f"⚡ LANGUAGE LOCKED: {lang_name} ({lang_code}) in {time.time() - start_time:.3f}s")
                    if current_call_id:
                        call_logger.set_language(current_call_id, f"{lang_name} ({lang_code})")
                    self.instructions = get_language_specific_instructions(lang_code, lang_config)
            else:
                lang_code = session_language
                lang_name = session_language_config.get('name', 'Unknown') if session_language_config else 'Unknown'
                logging.info(f"🔒 USING LOCKED LANGUAGE: {lang_name}")

        response = await super()._handle_user_message(message)

        if response and hasattr(response, 'content'):
            content = response.content
            if "FINAL_ANSWER:" in content:
                answer_part = content.split("FINAL_ANSWER:", 1)[1].strip()
                if answer_part.startswith("[") and "]" in answer_part:
                    answer_part = answer_part.split("]", 1)[1].strip()
                response.content = answer_part
                if transcription_logger:
                    transcription_logger.log_agent_response(answer_part)
                if current_call_id:
                    call_logger.log_agent_response(current_call_id, answer_part)

        return response
#__________UTILITY API ENDPOINTS_______________________________
@app.get("/api/export-logs")
async def export_logs():
    """Export all logs as a downloadable file"""
    try:
        call_logs_dir = Path("call_logs")
        if not call_logs_dir.exists():
            return {"status": "success", "message": "No logs to export"}

        # Create a summary of all logs
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "logs": []
        }

        for log_file in call_logs_dir.glob("*.txt"):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                file_stats = log_file.stat()

                export_data["logs"].append({
                    "filename": log_file.name,
                    "content": content,
                    "created_time": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                    "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                    "size": file_stats.st_size
                })

            except Exception as e:
                logging.error(f"Error reading log file {log_file}: {e}")
                continue

        return {"status": "success", "export_data": export_data}

    except Exception as e:
        logging.error(f"Error exporting logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Utility endpoints
@app.delete("/api/clear-logs")
async def clear_logs():
    """Clear all call logs (use with caution)"""
    try:
        call_logs_dir = Path("call_logs")
        if not call_logs_dir.exists():
            return {"status": "success", "message": "No logs to clear"}

        deleted_count = 0
        for log_file in call_logs_dir.glob("*.txt"):
            try:
                log_file.unlink()
                deleted_count += 1
            except Exception as e:
                logging.error(f"Error deleting log file {log_file}: {e}")
                continue

        return {
            "status": "success",
            "message": f"Cleared {deleted_count} log files",
            "deleted_count": deleted_count
        }

    except Exception as e:
        logging.error(f"Error clearing logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Catch-all route to serve React app (must be last to avoid interfering with API routes)
@app.get("/{full_path:path}")
async def serve_react_app(full_path: str):
    """Serve React app for all non-API routes"""

    # Don't interfere with API routes
    if full_path.startswith("api/"):
        raise HTTPException(status_code=404, detail="API endpoint not found")

    # Check if React build exists
    if not os.path.exists("dist/index.html"):
        raise HTTPException(
            status_code=500,
            detail="React build files not found. Run 'npm run build' first."
        )

    # Serve the React index.html for all routes (client-side routing)
    return FileResponse("dist/index.html")


# ---------- Entrypoint ----------
async def entrypoint(ctx: JobContext):
    """Enhanced entrypoint with proper call lifecycle and transcription logging."""
    global session_language, session_language_config, current_call_id, transcription_logger
    try:
        print("🚀 Starting enhanced voice agent with transcription logging...")
        start_time = time.time()

        await ctx.connect()

        session_language = None
        session_language_config = None
        current_call_id = None

        transcription_logger = TranscriptionLogger()

        print("⏳ Waiting for participant...")
        participant = await ctx.wait_for_participant()

        is_sip_call = hasattr(participant, 'kind') and participant.kind == rtc.ParticipantKind.PARTICIPANT_KIND_SIP

        if is_sip_call:
            current_call_id = call_logger.start_call(participant)
            if current_call_id:
                print(f"📞 SIP Call detected - Call ID: {current_call_id}")
                print(f"📱 Caller: {participant.attributes.get('sip.phoneNumber', 'Unknown')}")
                transcription_logger.start_call_transcription(current_call_id)
                logging.info(f"Call logging started for call ID: {current_call_id}")
            else:
                print("⚠️ Failed to start call logging")
        else:
            print("🖥️ Console/Web participant detected - Starting console transcription")
            transcription_logger.start_console_transcription()

        if rag_agent is not None:
            print("✅ Using preloaded RAG agent - no initialization delay!")
        else:
            print("⚠️ RAG agent not preloaded - may experience delays during first use")

        if vector_tool_instance is not None:
            print("✅ Using preloaded vector database tool")
        else:
            print("⚠️ Vector tool not preloaded")

        if web_tool_instance is not None:
            print("✅ Using preloaded web search tool")
        else:
            print("⚠️ Web tool not preloaded")

        vector_tool = create_vector_database_tool()
        web_tool = create_web_search_tool()

        stt_model = MODEL_CONFIG.get("stt", {}).get("model", "whisper-large-v3")
        llm_model = MODEL_CONFIG.get("llm", {}).get("model", "llama3-70b-8192")
        tts_model = MODEL_CONFIG.get("tts", {}).get("model", "sonic-2")

        stt_api_key = MODEL_CONFIG.get("stt", {}).get("api_key") or os.getenv("GROQ_API_KEY")
        llm_api_key = MODEL_CONFIG.get("llm", {}).get("api_key") or os.getenv("GROQ_API_KEY")
        tts_api_key = MODEL_CONFIG.get("tts", {}).get("api_key") or os.getenv("CARTESIA_API_KEY")

        print(f"🎤 Using STT model: {stt_model}")
        print(f"🧠 Using LLM model: {llm_model}")
        print(f"🔊 Using TTS model: {tts_model}")

        session = EnhancedCallLoggingSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model=stt_model,
                detect_language=True,
                api_key=stt_api_key
            ),
            llm=groq.LLM(
                model=llm_model,
                temperature=0.05,
                api_key=llm_api_key
            ),
            tts=cartesia.TTS(
                model=tts_model,
                api_key=tts_api_key
            ),
        )

        agent = UltraFastLanguageAgent(
            llm=session.llm,
            tools=[vector_tool, web_tool]
        )

        await session.start(agent=agent, room=ctx.room)

        print(f"✅ Agent ready in {time.time() - start_time:.2f}s")

        await session.generate_reply(
            instructions="Say: 'Hello! How can I help?' Keep it under 6 words. Do not mention tools."
        )

        print("🎙️ Session active - waiting for conversation...")
        try:
            while session.call_active:
                await asyncio.sleep(1)
        except Exception as e:
            logging.error(f"Session monitoring error: {e}")
        finally:
            await session.aclose()

    except Exception as e:
        logging.error(f"Voice agent error: {e}")
        raise CustomException(e, sys)
    finally:
        if transcription_logger:
            transcription_logger.close_transcription()
        if current_call_id:
            call_logger.end_call(current_call_id)
            print(f"📞 Call logging ended for call ID: {current_call_id}")
            logging.info(f"Call logging ended for call ID: {current_call_id}")

def run_fastapi_server():
    """Run FastAPI server in a separate thread"""
    print("🚀 Starting AI Voice System Backend...")
    print(f"📁 Working directory: {os.getcwd()}")
    print(f"⚙️  Config file: config/model_settings.json")

    # Create logs directory if it doesn't exist
    os.makedirs("call_logs", exist_ok=True)

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False,  # Set to False to avoid conflicts
        log_level="info"
    )

# ---------- Main launcher ----------
if __name__ == "__main__":
    # IMPORTANT: do the heavy initialization here so it's executed only once in the main process
    from livekit.agents import cli, WorkerOptions
    #start fastapi
    fastapi_thread = threading.Thread(target=run_fastapi_server, daemon=True)
    fastapi_thread.start()

    # create logs directory
    os.makedirs("call_logs", exist_ok=True)

    # initialize rag and tools BEFORE starting LiveKit workers
    initialize_rag()
    precreate_tools()

    print("🎙️ Starting LiveKit Agent... (main process initialized RAG & tools)")

    # Launch CLI + workers with our entrypoint. Worker processes will import this module
    # but won't re-run the main block above.
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, agent_name="telephony_agent"))
